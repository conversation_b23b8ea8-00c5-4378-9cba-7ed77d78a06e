import { Component, OnInit, ViewChild } from '@angular/core';
import { NavigationEnd, Router, RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';
import { SidebarComponent } from './shared';
import { filter } from 'rxjs/operators';
import { RouteService } from './core';

@Component({
  selector: 'chm-root',
  imports: [CommonModule, RouterOutlet, SidebarComponent],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css',
})
export class AppComponent implements OnInit {
  title = 'platform';
  hasActiveRoute = false;
  showSidebar = false;
  showSettingsPanel = false;
  sidebarCollapsed = true;
  nestedSidebarCollapsed = false;

  @ViewChild('sidebar') sidebar!: SidebarComponent;

  constructor(
    private router: Router,
    private routeService: RouteService,
  ) {
    // Check if there's an active route
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe((event: NavigationEnd) => {
        this.hasActiveRoute = event.url !== '/';
        this.showSidebar = !event.url.startsWith('/auth');
      });
  }

  ngOnInit() {
    // Initialize dynamic routes based on config
    console.log('🚀 Initializing dynamic routes...');
    this.routeService.updateRoutes();
  }

  toggleMobileSidebar() {
    console.log('🍔 Mobile hamburger clicked');
    if (this.sidebar) {
      this.sidebar.toggleSidebar();
    } else {
      console.warn('⚠️ Sidebar reference not found');
    }
  }

  onSidebarStateChange(state: { collapsed: boolean; showSettings: boolean; nestedCollapsed?: boolean }) {
    this.sidebarCollapsed = state.collapsed;
    this.showSettingsPanel = state.showSettings;
    if (state.nestedCollapsed !== undefined) {
      this.nestedSidebarCollapsed = state.nestedCollapsed;
    }
  }

  get mainContentClass() {
    let marginLeft = 280; // Default main sidebar width

    if (this.sidebarCollapsed) {
      marginLeft = 70; // Collapsed main sidebar width
    }

    if (this.showSettingsPanel) {
      if (this.nestedSidebarCollapsed) {
        marginLeft += 60; // Add collapsed nested sidebar width
      } else {
        marginLeft += 280; // Add full nested sidebar width
      }
    }

    return { 'margin-left': `${marginLeft}px` };
  }
}
